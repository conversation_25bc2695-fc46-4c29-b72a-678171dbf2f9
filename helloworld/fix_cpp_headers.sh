#!/bin/bash

# 修复C++头文件路径问题的脚本

echo "正在检查C++头文件路径..."

# 检查目标目录是否存在
TARGET_DIR="/Library/Developer/CommandLineTools/usr/include/c++/v1"
SOURCE_DIR="/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1"

if [ ! -d "$TARGET_DIR" ]; then
    echo "目标目录不存在，正在创建..."
    sudo mkdir -p "$TARGET_DIR"
fi

# 检查源目录是否存在
if [ ! -d "$SOURCE_DIR" ]; then
    echo "错误：源目录 $SOURCE_DIR 不存在"
    exit 1
fi

# 创建符号链接
echo "正在创建符号链接..."
sudo ln -sf "$SOURCE_DIR"/* "$TARGET_DIR/"

echo "完成！现在VSCode应该能够找到C++头文件了。"
echo "请重启VSCode以使更改生效。"
