{"configurations": [{"name": "C/C++: 运行程序", "type": "cppdbg", "request": "launch", "program": "${fileDirname}/${fileBasenameNoExtension}", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "console": "integratedTerminal", "MIMode": "lldb", "preLaunchTask": "C/C++: clang++ 生成活动文件"}, {"name": "C/C++: 调试程序", "type": "cppdbg", "request": "launch", "program": "${fileDirname}/${fileBasenameNoExtension}", "args": [], "stopAtEntry": true, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "console": "integratedTerminal", "MIMode": "lldb", "preLaunchTask": "C/C++: clang++ 生成活动文件"}], "version": "2.0.0"}