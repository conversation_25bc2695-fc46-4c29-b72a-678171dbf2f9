{"configurations": [{"name": "<PERSON>", "includePath": ["${workspaceFolder}/**", "/Library/Developer/CommandLineTools/usr/include/c++/v1", "/Library/Developer/CommandLineTools/usr/lib/clang/17/include", "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include"], "defines": [], "macFrameworkPath": ["/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks"], "compilerPath": "/usr/bin/clang++", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "macos-clang-arm64"}], "version": 4}