#!/bin/bash

echo "🔧 修复VSCode C++ IntelliSense问题..."

# 检查是否有管理员权限
if [ "$EUID" -ne 0 ]; then
    echo "❌ 此脚本需要管理员权限来创建符号链接"
    echo "请运行: sudo ./fix_intellisense.sh"
    exit 1
fi

# 源目录和目标目录
SOURCE_DIR="/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1"
TARGET_DIR="/Library/Developer/CommandLineTools/usr/include/c++/v1"

echo "📁 检查目录..."
echo "源目录: $SOURCE_DIR"
echo "目标目录: $TARGET_DIR"

# 检查源目录是否存在
if [ ! -d "$SOURCE_DIR" ]; then
    echo "❌ 错误: 源目录不存在: $SOURCE_DIR"
    exit 1
fi

# 检查目标目录是否存在
if [ ! -d "$TARGET_DIR" ]; then
    echo "❌ 错误: 目标目录不存在: $TARGET_DIR"
    exit 1
fi

# 检查iostream文件是否存在于源目录
if [ ! -f "$SOURCE_DIR/iostream" ]; then
    echo "❌ 错误: iostream文件不存在于源目录"
    exit 1
fi

echo "✅ 目录检查通过"

# 备份现有文件（如果存在）
echo "💾 备份现有文件..."
BACKUP_DIR="/tmp/cpp_headers_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# 复制现有的v1目录内容到备份
cp -r "$TARGET_DIR" "$BACKUP_DIR/" 2>/dev/null || true
echo "备份保存在: $BACKUP_DIR"

# 删除目标目录中的现有文件
echo "🗑️  清理目标目录..."
rm -rf "$TARGET_DIR"/*

# 创建符号链接
echo "🔗 创建符号链接..."
ln -sf "$SOURCE_DIR"/* "$TARGET_DIR/"

# 验证链接是否成功
if [ -f "$TARGET_DIR/iostream" ]; then
    echo "✅ 符号链接创建成功!"
    echo "📝 验证文件:"
    ls -la "$TARGET_DIR/iostream"
else
    echo "❌ 符号链接创建失败"
    exit 1
fi

echo ""
echo "🎉 修复完成!"
echo "现在请:"
echo "1. 重启VSCode"
echo "2. 重新打开你的C++项目"
echo "3. IntelliSense应该能正常工作了"
